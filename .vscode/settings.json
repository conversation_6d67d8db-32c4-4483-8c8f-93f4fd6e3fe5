{"dotnet.defaultSolution": "WHO.MALARIA.sln", "omnisharp.enableEditorConfigSupport": true, "omnisharp.enableRoslynAnalyzers": true, "files.exclude": {"**/bin": true, "**/obj": true, "**/node_modules": true}, "search.exclude": {"**/bin": true, "**/obj": true, "**/node_modules": true, "**/build": true}, "typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "javascript.suggest.autoImports": true, "emmet.includeLanguages": {"javascript": "javascriptreact", "typescript": "typescriptreact"}, "debug.console.fontSize": 14, "debug.console.lineHeight": 20, "debug.internalConsoleOptions": "neverOpen", "debug.terminal.clearBeforeReusing": true, "csharp.semanticHighlighting.enabled": true, "csharp.format.enable": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "[csharp]": {"editor.defaultFormatter": "ms-dotnettools.csharp"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true}