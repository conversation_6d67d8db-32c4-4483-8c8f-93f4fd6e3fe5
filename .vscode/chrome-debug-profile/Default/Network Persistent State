{"net": {"http_server_properties": {"servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396886456869127", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 166345}, "server": "https://fonts.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396886461530487", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396886461291225", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 78879}, "server": "https://fonts.gstatic.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396888819379206", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABEAAABodHRwczovL2xvY2FsaG9zdAAAAA==", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"anonymization": ["IAAAABsAAABodHRwczovL21pY3Jvc29mdG9ubGluZS5jb20A", false, 0], "server": "https://aadcdn.msftauth.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396888826328024", "port": 443, "protocol_str": "quic"}], "anonymization": ["IAAAABsAAABodHRwczovL21pY3Jvc29mdG9ubGluZS5jb20A", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396888845307471", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACwAAABodHRwczovL3Bhc3N3b3Jkc2xlYWtjaGVjay1wYS5nb29nbGVhcGlzLmNvbQ==", false, 0], "server": "https://passwordsleakcheck-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396888848107953", "port": 443, "protocol_str": "quic"}], "anonymization": ["LAAAACcAAABodHRwczovL2NvbnRlbnQtYXV0b2ZpbGwuZ29vZ2xlYXBpcy5jb20A", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396888848176408", "port": 443, "protocol_str": "quic"}], "anonymization": ["IAAAABoAAABodHRwczovL3d3dy5nb29nbGVhcGlzLmNvbQAA", false, 0], "server": "https://www.googleapis.com", "supports_spdy": true}, {"anonymization": ["IAAAABsAAABodHRwczovL21pY3Jvc29mdG9ubGluZS5jb20A", false, 0], "server": "https://aadcdn.msauth.net", "supports_spdy": true}, {"anonymization": ["IAAAABsAAABodHRwczovL21pY3Jvc29mdG9ubGluZS5jb20A", false, 0], "server": "https://eu-mobile.events.data.microsoft.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABkZXZ0b29sczovL2RldnRvb2xzAA==", false, 0], "server": "https://localhost:5001", "supports_spdy": true}, {"anonymization": ["IAAAABsAAABodHRwczovL21pY3Jvc29mdG9ubGluZS5jb20A", false, 0], "server": "https://login.microsoftonline.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396888957883434", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABEAAABodHRwczovL2xvY2FsaG9zdAAAAA==", false, 0], "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"anonymization": ["IAAAABsAAABodHRwczovL21pY3Jvc29mdG9ubGluZS5jb20A", false, 0], "server": "https://identity.nel.measure.office.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL2dzdGF0aWMuY29tAA==", false, 0], "server": "https://encrypted-tbn0.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 92105}, "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "network_stats": {"srtt": 77167}, "server": "https://optimizationguide-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABEAAABodHRwczovL2xvY2FsaG9zdAAAAA==", false, 0], "network_stats": {"srtt": 166654}, "server": "https://fonts.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL2xvY2FsaG9zdAAAAA==", false, 0], "server": "https://localhost:5001", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABEAAABodHRwczovL2xvY2FsaG9zdAAAAA==", false, 0], "network_stats": {"srtt": 126292}, "server": "https://fonts.gstatic.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396893338253800", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 78793}, "server": "https://www.google.com"}], "supports_quic": {"address": "************", "used_quic": true}, "version": 5}, "network_qualities": {"CAISABiAgICA+P////8B": "4G"}}}