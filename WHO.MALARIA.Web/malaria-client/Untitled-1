{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/${input:projectFolder}"], "problemMatcher": "$msCompile"}], "inputs": [{"id": "projectFolder", "type": "pickString", "description": "Select the project folder to build", "options": ["WHO.MALARIA.Common", "WHO.MALARIA.Database", "WHO.MALARIA.DocumentManager", "WHO.MALARIA.Domain", "WHO.MALARIA.Features", "WHO.MALARIA.Services", "WHO.MALARIA.Web"]}]}